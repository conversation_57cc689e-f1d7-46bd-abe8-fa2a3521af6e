#POETRY

[tool.poetry]
name = "performance-analytics-counter-report-service"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
wrapt = "^1"
aws-xray-sdk = "^2"
lib-performance-analytics = "^33"
lib-sysxds-common = "^0"
ft2-cloud-sdk = "^16"
aws-lambda-powertools = "^2"
fastapi = "^0"
mangum = "^0"
requests = "^2.32.3"
secweb = "^1.11.0"

[tool.poetry.group.dev.dependencies]
mock = "*"
pre-commit = "*"
boto3 = "^1.35"
pylint = "^3"
pytest = "^7"
pytest-cov = "*"
black = "^23"
flake8 = "^6"
uvicorn = "^0.27" # pin to that version due to "h11" not beeing installed otherwise
pytest-mock = "^3.14.0"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

#BLACK

[tool.black]
line-length = 100

#PYTEST

[tool.pytest.ini_options]
addopts = " -p no:cacheprovider -vv -rf --strict --durations 10 --color yes"
filterwarnings = [
  "error",
  "ignore::DeprecationWarning",
  "ignore::PendingDeprecationWarning",
  "ignore::ImportWarning",
  "ignore::pytest.PytestUnraisableExceptionWarning"
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = "./src"

#COVERAGE

[tool.coverage.run]
branch = true
omit = [
  "test/*",
  "*/__init__.py",
  "*/_version.py",
]

[tool.coverage.report]
precision = 2
fail_under = 0
