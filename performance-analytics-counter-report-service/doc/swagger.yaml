swagger: "2.0"
info:
  version: "1.0.0"
  title: "Swagger Units Report"
basePath: "/v1"
tags:
- name: "report"
schemes:
- "https"
paths:
  /performance-analytics/units-report/{lineId}/{equiptmentId}:
    get:
      tags:
      - "report"
      summary: "Get a single report for equipment"
      description: "Returns a single production report for equipment"
      operationId: "getEquipmentReportById"
      produces:
      - "application/json"
      parameters:
      - name: "lineId"
        in: "path"
        description: "line id"
        required: true
        type: "string"
      - name: "equiptmentId"
        in: "path"
        description: "report of the eqipment to return"
        required: true
        type: "string"
      - name: "time_from"
        in: "query"
        description: "time from which to return report"
        required: true
        type: "integer"
        format: "int64"
      - name: "time_to"
        in: "query"
        description: "time to until to return report"
        required: true
        type: "integer"
        format: "int64"
      responses:
        "200":
          description: "successful operation"
          schema:
            $ref: "#/definitions/units_report"
        "400":
          description: "Invalid ID supplied"
        "404":
          description: "Report not found"

definitions:
  units_report:
    type: "object"
    required:
    - "customer"
    - "eq_id"
    - "time_from"
    - "time_to"
    - "units_produced"
    - "units_defect"
    - "units_total"
    - "sender"
    properties:
      customer:
        type: "string"
      eq_id:
        type: "string"
      time_from:
        type: "integer"
        format: "int64"
      time_to:
        type: "integer"
        format: "int64"
      units_produced:
        type: "integer"
        format: "int64"
        x-nullable: true
      units_defect:
        type: "integer"
        format: "int64"
        x-nullable: true
      units_total:
        type: "integer"
        format: "int64"
        x-nullable: true
      sender:
        type: "object"
        properties:
          name:
            type: "string"
          version:
            type: "string"
