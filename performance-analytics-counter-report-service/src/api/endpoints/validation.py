# Copyright (c) 2021, Syskron GmbH. All rights reserved.

from http import HTTPStatus
from typing import Any

from aws_lambda_powertools import Logger
from lib_cloud_sdk.api_gateway.responses import build_error

LOGGER = Logger()

Response = dict[str, Any]


def validate_timestamp(
    param_name: str, value: str | None, is_optional: bool = False
) -> int | None | Response:
    """Validate timestamp parameter.

    Args:
        param_name: Name of the parameter being validated
        value: The timestamp value to validate
        is_optional: Whether the parameter is optional

    Returns:
        int: Validated timestamp as integer
        None: If parameter is optional and not provided
        Response: Error response if validation fails
    """
    if not value:
        if is_optional:
            return None
        LOGGER.error('Abort get of units-report! Missing query_parameter "%s"', param_name)
        return build_error(HTTPStatus.BAD_REQUEST, f'Missing query_parameter "{param_name}"')

    if not isvalid_timestamp(value):
        LOGGER.error('invalid timestamp for %s="%s"', param_name, value)
        return build_error(HTTPStatus.BAD_REQUEST, f'Invalid timestamp for "{param_name}"')

    return int(value)


def isvalid_timestamp(timestamp: str) -> bool:
    """Check if a timestamp string is valid.
    
    Args:
        timestamp: The timestamp string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        int(timestamp)
        return True
    except ValueError:
        return False
