# Copyright (c) 2021, Syskron GmbH. All rights reserved.

from typing import Optional
from aws_lambda_powertools import Logger, Trace<PERSON>
from fastapi import APIRouter, HTTPException, Path, Query, Request, status, Depends
from lib_cloud_sdk.util.common import validate_fast_api_timerange
from performance_analytics.fast_api.dependencies.s2a_properties import Share2ActProperties

from api.endpoints.models import CounterReportResponse
from api.endpoints.units_service import _get_block_counter_report, _get_machine_counter_report

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


# pylint: disable=too-many-positional-arguments,too-many-arguments
@router.get(
    "/v2/{line_id}",
    response_model=CounterReportResponse,
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def line_counter_report(
    request: Request,
    properties: Share2ActProperties,
    line_id: str = Path(..., description="Line identifier"),
    time_from: int = Query(..., description="Start timestamp in milliseconds"),
    time_to: Optional[int] = Query(
        None, description="End timestamp in milliseconds (optional, defaults to current time)"
    ),
) -> CounterReportResponse:
    """
    Get counter report for a specific line using block-level counters.

    Returns units produced, defective units, and total units for the specified timerange.
    Uses block-level counters for line-level reporting.
    """
    try:
        LOGGER.info(
            "Processing line counter report request",
            extra={
                "line_id": line_id,
                "time_from": time_from,
                "time_to": time_to,
                "account": properties.account,
            },
        )

        # Set default time_to to current timestamp if not provided
        if time_to is None:
            import time

            time_to = int(time.time() * 1000)

        # Prepare base response
        base_response = {
            "customer": properties.account,
            "eq_id": line_id,
            "time_from": time_from,
            "time_to": time_to,
        }

        # Call _get_block_counter_report directly for line-level reporting
        result = _get_block_counter_report(
            base_response=base_response,
            line_id=line_id,
            machine_id=line_id,  # Use line_id as machine_id for block counters
            account=properties.account,
            time_from=time_from,
            time_to=time_to,
        )

        LOGGER.info(
            "Line counter report request completed successfully",
            extra={
                "line_id": line_id,
                "units_produced": result.units_produced,
                "units_defect": result.units_defect,
                "units_total": result.units_total,
            },
        )

        return result

    except HTTPException as http_excep:
        LOGGER.warning(
            "line_counter_report failed",
            extra={
                "raised_exception": http_excep,
                "event": request.scope.get("aws.event"),
                "line_id": line_id,
            },
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "line_counter_report failed",
            extra={
                "exception_type": type(excep),
                "raised_exception": excep,
                "event": request.scope.get("aws.event"),
                "line_id": line_id,
            },
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing line counter report",
        ) from excep


# pylint: disable=too-many-positional-arguments,too-many-arguments
@router.get(
    "/v2/{line_id}/{machine_id}",
    response_model=CounterReportResponse,
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def machine_counter_report(
    request: Request,
    properties: Share2ActProperties,
    line_id: str = Path(..., description="Line identifier"),
    machine_id: str = Path(..., description="Machine identifier"),
    time_from: int = Query(..., description="Start timestamp in milliseconds"),
    time_to: Optional[int] = Query(
        None, description="End timestamp in milliseconds (optional, defaults to current time)"
    ),
) -> CounterReportResponse:
    """
    Get counter report for a specific machine using machine-level counters.

    Returns units produced, defective units, and total units for the specified timerange.
    Uses machine-level counters for machine-specific reporting.
    """
    try:
        LOGGER.info(
            "Processing machine counter report request",
            extra={
                "line_id": line_id,
                "machine_id": machine_id,
                "time_from": time_from,
                "time_to": time_to,
                "account": properties.account,
            },
        )

        # Set default time_to to current timestamp if not provided
        if time_to is None:
            import time

            time_to = int(time.time() * 1000)

        # Prepare base response
        base_response = {
            "customer": properties.account,
            "eq_id": machine_id,
            "time_from": time_from,
            "time_to": time_to,
        }

        # Call _get_machine_counter_report directly for machine-level reporting
        result = _get_machine_counter_report(
            base_response=base_response,
            machine_id=machine_id,
            account=properties.account,
            time_from=time_from,
            time_to=time_to,
        )

        LOGGER.info(
            "Machine counter report request completed successfully",
            extra={
                "line_id": line_id,
                "machine_id": machine_id,
                "units_produced": result.units_produced,
                "units_defect": result.units_defect,
                "units_total": result.units_total,
            },
        )

        return result

    except HTTPException as http_excep:
        LOGGER.warning(
            "machine_counter_report failed",
            extra={
                "raised_exception": http_excep,
                "event": request.scope.get("aws.event"),
                "line_id": line_id,
                "machine_id": machine_id,
            },
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "machine_counter_report failed",
            extra={
                "exception_type": type(excep),
                "raised_exception": excep,
                "event": request.scope.get("aws.event"),
                "line_id": line_id,
                "machine_id": machine_id,
            },
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing machine counter report",
        ) from excep
