{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Resources of the performance analytics service.", "Transform": "AWS::Serverless-2016-10-31", "Resources": {"CombinedLambdaVPCAccessAndXRayPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "CombinedLambdaVPCAccessAndXRayPolicy", "Description": "Combines permissions from AWSLambdaVPCAccessExecutionRole and AWSXrayWriteOnlyAccess", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "LambdaVPCAccessPermissions", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DescribeSubnets", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"], "Resource": "*"}, {"Sid": "XRayPermissions", "Effect": "Allow", "Action": ["xray:PutTraceSegments", "xray:PutTelemetryRecords", "xray:GetSamplingRules", "xray:GetSamplingTargets", "xray:GetSamplingStatisticSummaries"], "Resource": "*"}]}}}, "LambdaPerformanceAnalyticsService": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "performance-analytics-service", "Role": {"Fn::GetAtt": ["PerformanceAnalyticsServiceRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"SERVICE": "s2a-performance"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "MemorySize": 2048, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaPerformanceAnalyticsServiceDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "performance-analytics-service", "S2A_SHIFT_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ARN": {"Fn::Join": [":", ["arn:aws:iam:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "role/s2a-shift-service-cross-account-access-role"]]}, "S2A_SHIFT_SERVICE_LAMBDA_ARN": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-get"]]}, "AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, "RK_AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:StageRK"}, "S2A_AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:StageS2A"}, "S2A_AWS_ACCOUNT": {"Fn::ImportValue": "performance-basic-infrastructure:AWSAccountIdS2A"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}}}, "GetMessageTextPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "performance-analytics-message-text-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "GetSSMParametersStatement", "Action": "ssm:GetParameter", "Effect": "Allow", "Resource": "*"}, {"Sid": "AssumeRoleSSMCrossAccountStatement", "Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Resource": [{"Ref": "MessageTextCrossAccountSSMRoleARN"}]}, {"Sid": "ReadMessageTextDynamoDBStatement", "Effect": "Allow", "Action": ["dynamodb:GetItem", "dynamodb:Query"], "Resource": [{"Fn::ImportValue": "performance-unified-translation-cache:MessageTextTableARN"}, {"Fn::Join": ["", [{"Fn::ImportValue": "performance-unified-translation-cache:MessageTextTableARN"}, "/index/*"]]}]}, {"Sid": "MessageTextQueueUpdateRequestStatement", "Effect": "Allow", "Action": ["sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:SendMessage"], "Resource": {"Fn::ImportValue": "performance-unified-translation-cache:MessageTextUpdateQueueARN"}}, {"Sid": "GetMessageTextJsonS3Statement", "Effect": "Allow", "Action": ["s3:GetObject"], "Resource": {"Fn::Join": ["", ["arn:aws:s3:::rk-zait-", {"Fn::ImportValue": "rk-footprint:stage"}, "/*"]]}}]}}}, "PerformanceAnalyticsServiceRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "performance-analytics-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Fn::ImportValue": "rk-footprint:config-generator-meda-api-key-access-policy-arn"}, {"Fn::ImportValue": "kpi-config-service:lib-kpi-config-client-managed-read-policy"}, {"Fn::ImportValue": "downtime-service:ManagedPerformanceDowntimesPolicyARN"}, {"Fn::ImportValue": "downtime-service:ManagedMessagesPolicyARN"}, {"Fn::ImportValue": "correction-pipeline:ManagedCorrectionPipelinePolicyARN"}, {"Ref": "PerformanceAnalyticsServiceDynamoDbFailureModePolicy"}, {"Fn::ImportValue": "downtime-service:ManagedQueryPerformanceDataPolicyARN"}, {"Ref": "AssumeRoleShiftServicePolicy"}]}}, "PerformanceAnalyticsKpiServiceRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "performance-analytics-kpi-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": [{"Ref": "CombinedLambdaVPCAccessAndXRayPolicy"}, {"Fn::ImportValue": "rk-footprint:config-generator-meda-api-key-access-policy-arn"}, {"Fn::ImportValue": "performance-meda-cache:lib-meda-cache-managed-read-policy"}, {"Fn::ImportValue": "kpi-config-service:lib-kpi-config-client-managed-read-policy"}, {"Ref": "AssumeRoleShiftServicePolicy"}, {"Fn::ImportValue": "performance-equipment-cache:EquipmentClientPolicy"}, {"Fn::ImportValue": "downtime-service:ManagedMessagesPolicyARN"}, {"Fn::ImportValue": "correction-pipeline:ManagedCorrectionPipelinePolicyARN"}, {"Fn::ImportValue": "downtime-service:ManagedPerformanceDowntimesPolicyARN"}, {"Fn::ImportValue": "downtime-service:ManagedQueryPerformanceDataPolicyARN"}], "Policies": [{"PolicyName": "CustomerSettingsTableInlinePolicy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:Query", "dynamodb:UpdateItem"], "Resource": {"Fn::GetAtt": ["CustomerSettingsTable", "<PERSON><PERSON>"]}}]}}]}}, "PerformanceAnalyticsMachineReportPublicAPIRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "performance-analytics-machine-report-public-api-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": [{"Ref": "CombinedLambdaVPCAccessAndXRayPolicy"}, {"Fn::ImportValue": "rk-footprint:config-generator-meda-api-key-access-policy-arn"}, {"Fn::ImportValue": "performance-meda-cache:lib-meda-cache-managed-read-policy"}, {"Fn::ImportValue": "kpi-config-service:lib-kpi-config-client-managed-read-policy"}, {"Fn::ImportValue": "performance-equipment-cache:EquipmentClientPolicy"}, {"Fn::ImportValue": "downtime-service:ManagedMessagesPolicyARN"}, {"Fn::ImportValue": "downtime-service:ManagedPerformanceDowntimesPolicyARN"}, {"Fn::ImportValue": "downtime-service:ManagedQueryPerformanceDataPolicyARN"}], "Policies": [{"PolicyName": "CustomerSettingsTableInlinePolicy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:Query", "dynamodb:UpdateItem"], "Resource": {"Fn::GetAtt": ["CustomerSettingsTable", "<PERSON><PERSON>"]}}]}}]}}, "PerformanceAnalyticsServiceFaultDynamoDbPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "performance-analytics-service-fault-dynamo-db-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:Query"], "Resource": [{"Fn::ImportValue": {"Fn::Sub": "${DataStorageDynamoDB}:dynamodb-performance-data-arn"}}]}]}}}, "PerformanceAnalyticsServiceDynamoDbFailureModePolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "performance-analytics-service-failure-mode-dynamo-db-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:Query"], "Resource": [{"Fn::Join": [":", ["arn:aws:dynamodb", {"Ref": "AWS::Region"}, {"Ref": "AWS::AccountId"}, "table/rk-failure-mode-table"]]}]}]}}}, "LambdaPerformanceAnalyticsServicePermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaPerformanceAnalyticsServiceAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "LambdaCustomerSettings": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "customer-settings-service", "Role": {"Fn::GetAtt": ["CustomerSettingsRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"SERVICE": "s2a-performance"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaCustomerSettingsDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "customer-settings-service", "AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}}}, "CustomerSettingsRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "customer-settings-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Ref": "DynamoCustomerSettingsTablePolicy"}]}}, "LambdaCustomerSettingsPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaCustomerSettingsAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "LambdaThreshold": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "threshold-service", "Role": {"Fn::GetAtt": ["ThresholdRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"SERVICE": "s2a-performance"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaThresholdDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "threshold-service", "AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}}}, "ThresholdRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "threshold-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Ref": "DynamoThresholdTablePolicy"}]}}, "LambdaThresholdPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaThresholdAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "LambdaUnitsReportService": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "performance-analytics-units-service", "Role": {"Fn::GetAtt": ["UnitsReportServiceRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "MemorySize": 2048, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"SERVICE": "s2a-performance"}, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaUnitsReportServiceDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "performance-analytics-units-service", "AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}}}, "UnitsReportServiceRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "units-report-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Fn::ImportValue": "performance-equipment-cache:EquipmentClientPolicy"}, {"Fn::ImportValue": "performance-meda-cache:lib-meda-cache-managed-read-policy"}, {"Ref": "PerformanceAnalyticsServiceFaultDynamoDbPolicy"}]}}, "LambdaUnitsReportServicePermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaUnitsReportServiceAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "LambdaProductsSpeeds": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "products-speeds-service", "Role": {"Fn::GetAtt": ["ProductsSpeedsRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"SERVICE": "s2a-performance"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "MemorySize": 2048, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaProductsSpeedsDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "products-speeds-service", "AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}}}, "ProductsSpeedsRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "products-speeds-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Ref": "DynamoProductsSpeedsTablePolicy"}, {"Ref": "DynamoCustomerSettingsTablePolicy"}, {"Ref": "ProductsSpeedsS3Policy"}]}}, "LambdaProductsSpeedsPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaProductsSpeedsAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "LambdaPerformanceAnalyticsKpiService": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "performance-analytics-kpi-service", "Role": {"Fn::GetAtt": ["PerformanceAnalyticsKpiServiceRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 50, "MemorySize": 2048, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"NAME": "kpi-service", "SERVICE": "s2a-performance"}, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaKpiServiceDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "performance-analytics-kpi-service", "S2A_SHIFT_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ARN": {"Fn::Join": [":", ["arn:aws:iam:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "role/s2a-shift-service-cross-account-access-role"]]}, "S2A_SHIFT_SERVICE_LAMBDA_ARN": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-get"]]}, "AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, "RK_AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:StageRK"}, "S2A_AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:StageS2A"}, "S2A_AWS_ACCOUNT": {"Fn::ImportValue": "performance-basic-infrastructure:AWSAccountIdS2A"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}}}, "LambdaPerformanceAnalyticsMachineReportPublicAPI": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "performance-analytics-machine-report-public-api", "Role": {"Fn::GetAtt": ["PerformanceAnalyticsMachineReportPublicAPIRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 50, "MemorySize": 2048, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"NAME": "performance-analytics-machine-report-public-api", "SERVICE": "s2a-performance"}, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaKpiServicePublicAPIDeploy"}}, "Handler": "api.public.lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "performance-analytics-kpi-service-public-api", "AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, "RK_AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:StageRK"}, "S2A_AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:StageS2A"}, "S2A_AWS_ACCOUNT": {"Fn::ImportValue": "performance-basic-infrastructure:AWSAccountIdS2A"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}}}, "PerformanceAnalyticsMachineReportPublicAPIPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaPerformanceAnalyticsMachineReportPublicAPI"}, "Principal": "apigateway.amazonaws.com"}}, "LambdaPerformanceAnalyticsKpiServicePermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaPerformanceAnalyticsKpiServiceAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "LambdaPerformanceAnalyticsServiceConfigGenerator": {"Type": "AWS::Lambda::Function", "Properties": {"FunctionName": "performance-analytics-service-config-generator", "Role": {"Fn::GetAtt": ["ConfigGeneratorRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "Tags": [{"Key": "SERVICE", "Value": "s2a-performance"}], "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 60, "Code": {"S3Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "S3Key": {"Ref": "LambdaConfigGeneratorDeploy"}}, "Handler": "lambda_function.lambda_handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "performance-analytics-service-config-generator", "EVENTBRIDGE_NAME": {"Fn::ImportValue": "isc-global-eventbridge-eventbus-arn"}, "S3_OFFLOADING_BUCKET_NAME": {"Fn::ImportValue": "global-isc-eventbridge-offloadbucket"}, "S2A_EQUIPMENTS_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ANCESTORS_ARN": {"Fn::Join": [":", ["arn:aws:iam:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "role/s2a-equipments-get-ancestors-service-cross-account-access-role"]]}, "S2A_EQUIPMENTS_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ARN": {"Fn::Join": [":", ["arn:aws:iam:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "role/s2a-equipments-service-get-machines-cross-account-role"]]}, "S2A_SHIFT_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ARN": {"Fn::Join": [":", ["arn:aws:iam:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "role/s2a-shift-service-cross-account-access-role"]]}, "S2A_EQUIPMENTS_SERVICE_LAMBDA_ARN": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-equipments-s2a-read-ancestors"]]}, "S2A_SHIFT_SERVICE_CREATE_LAMBDA_ARN": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-create"]]}, "S2A_SHIFT_SERVICE_GET_LAMBDA_ARN": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-get"]]}, "S2A_SHIFT_SERVICE_UPDATE_LAMBDA_ARN": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-update"]]}, "AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}, "S2A_EQUIPMENTS_SERVICE_LAMBDA_GET_DESCENDANTS_ARN": {"Fn::Join": ["", ["arn:aws:lambda:eu-central-1:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, ":function:s2a-equipments-s2a-read-descendants"]]}, "S2A_EQUIPMENTS_SERVICE_LAMBDA_GET_PARENTS_ARN": {"Fn::Join": ["", ["arn:aws:lambda:eu-central-1:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, ":function:s2a-equipments-s2a-read-parents"]]}}}}}, "ConfigGeneratorRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "performance-analytics-service-config-generator-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", {"Ref": "DynamoCustomerSettingsTablePolicy"}, {"Fn::ImportValue": "rk-footprint:config-generator-database-access-policy-arn"}, {"Ref": "AssumeRoleShiftServicePolicy"}, {"Ref": "AssumeRoleEquipmentsServicePolicy"}, {"Fn::ImportValue": "kpi-config-service:lib-kpi-config-client-managed-read-write-policy"}, {"Fn::ImportValue": "performance-equipment-cache:EquipmentClientPolicy"}, {"Fn::ImportValue": "managed-policy-arn-for-publisher"}]}}, "AssumeRoleShiftServicePolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "performance-analytics-assume-role-shift-service-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Resource": {"Fn::Join": [":", ["arn:aws:iam:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "role/s2a-shift-service-cross-account-access-role"]]}}, {"Action": "lambda:InvokeFunction", "Effect": "Allow", "Resource": [{"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-create"]]}, {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-get"]]}, {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-shift-s2a-update"]]}]}]}}}, "AssumeRoleEquipmentsServicePolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "performance-analytics-assume-role-equipments-service-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Resource": {"Fn::Join": [":", ["arn:aws:iam:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "role/s2a-equipments-get-ancestors-service-cross-account-access-role"]]}}, {"Action": "lambda:InvokeFunction", "Effect": "Allow", "Resource": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, "function:s2a-equipments-s2a-read-ancestors"]]}}]}}}, "LambdaErrorList": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "error-list-service", "Role": {"Fn::GetAtt": ["ErrorListRole", "<PERSON><PERSON>"]}, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"Key": "SERVICE", "Value": "s2a-performance"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "MemorySize": 2000, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaErrorListDeploy"}}, "Handler": "main.handler", "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "error-list-service", "AWS_STAGE": {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, "MESSAGE_RECORDER_API_SECRET_NAME": "message-recorder-api-secret", "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}, "LoggingConfig": {"LogGroup": {"Ref": "ErrorListLogGroup"}, "LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}}}, "ErrorListLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/error-list-service-log-group", "RetentionInDays": 14, "KmsKeyId": {"Fn::Join": ["", ["arn:aws:kms:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "performance-cloudwatch-logs-kms-key-alias"}]]}}}, "LambdaComments": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "comments-service", "Role": {"Fn::GetAtt": ["CommentsRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"SERVICE": "s2a-performance"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "MemorySize": 2000, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaCommentsDeploy"}}, "Handler": "main.handler", "VpcConfig": {"SecurityGroupIds": [{"Fn::ImportValue": "performance-vpc-ClientSecurityGroup"}], "SubnetIds": {"Fn::Split": [",", {"Fn::ImportValue": "performance-vpc-SubnetsPrivate"}]}}, "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "comments-service", "AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "CLIENT_SECRET_ARN": {"Fn::ImportValue": {"Fn::Sub": "${RdsStackName}-V2ClientDatabaseUserSecretArn"}}, "DB_NAME": {"Fn::ImportValue": {"Fn::Sub": "${RdsStackName}-DatabaseName"}}, "EVENTBRIDGE_NAME": {"Fn::ImportValue": "isc-global-eventbridge-eventbus-arn"}, "S3_OFFLOADING_BUCKET_NAME": {"Fn::ImportValue": "global-isc-eventbridge-offloadbucket"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}, "S2A_EQUIPMENTS_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ARN": {"Fn::Join": ["", ["arn:aws:iam::", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, ":role/s2a-equipments-service-get-machines-cross-account-role"]]}, "S2A_EQUIPMENTS_SERVICE_LAMBDA_GET_DESCENDANTS_ARN": {"Fn::Join": ["", ["arn:aws:lambda:eu-central-1:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, ":function:s2a-equipments-s2a-read-descendants"]]}, "S2A_EQUIPMENTS_SERVICE_LAMBDA_GET_PARENTS_ARN": {"Fn::Join": ["", ["arn:aws:lambda:eu-central-1:", {"Fn::ImportValue": "rk-footprint:accountid-s2a"}, ":function:s2a-equipments-s2a-read-parents"]]}}}}}, "LambdaCommentsMigrate": {"Type": "AWS::Lambda::Function", "Properties": {"FunctionName": "comments-service-migrate", "Role": {"Fn::GetAtt": ["CommentsRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "Tags": [{"Key": "SERVICE", "Value": "s2a-performance"}], "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "MemorySize": 2000, "Code": {"S3Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "S3Key": {"Ref": "LambdaCommentsDeploy"}}, "Handler": "alembic.config.main", "VpcConfig": {"SecurityGroupIds": [{"Fn::ImportValue": "performance-vpc-ClientSecurityGroup"}], "SubnetIds": {"Fn::Split": [",", {"Fn::ImportValue": "performance-vpc-SubnetsPrivate"}]}}, "Environment": {"Variables": {"AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "CLIENT_SECRET_ARN": {"Fn::ImportValue": {"Fn::Sub": "${RdsStackName}-V2ClientDatabaseUserSecretArn"}}, "DB_NAME": {"Fn::ImportValue": {"Fn::Sub": "${RdsStackName}-DatabaseName"}}}}}}, "ErrorListRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "error-list-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Ref": "ErrorListSecretsManagerAccessPolicy"}]}}, "LambdaErrorListPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaErrorListAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "CommentsRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "comments-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Ref": "CommentsRdsClusterAccessPolicy"}, {"Fn::ImportValue": "managed-policy-arn-for-publisher"}, {"Fn::ImportValue": "performance-equipment-cache:EquipmentClientPolicy"}, {"Fn::ImportValue": "kpi-config-service:lib-kpi-config-client-managed-read-policy"}]}}, "LambdaCommentsPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaCommentsAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "ErrorListSecretsManagerAccessPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "error-list-secrets-manager-access-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "SecretsManagerMessageRecorderAccess", "Effect": "Allow", "Action": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:TagResource"], "Resource": {"Fn::Sub": "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:message-recorder-api-secret-*"}}]}}}, "CommentsRdsClusterAccessPolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "performance-comments-service-rds-aurora-cluster-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "SecretsManagerDbCredentialsAccess", "Effect": "Allow", "Action": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:TagResource"], "Resource": {"Fn::ImportValue": {"Fn::Sub": "${RdsStackName}-V2ClientDatabaseUserSecretArn"}}}]}}}, "LambdaCheckmatCurve": {"Type": "AWS::Serverless::Function", "Properties": {"FunctionName": "checkmat-curve-service", "Role": {"Fn::GetAtt": ["CheckmatCurveRole", "<PERSON><PERSON>"]}, "LoggingConfig": {"LogFormat": "JSON", "SystemLogLevel": "WARN", "ApplicationLogLevel": "WARN"}, "AutoPublishAlias": "live", "SnapStart": {"ApplyOn": "PublishedVersions"}, "Tags": {"SERVICE": "s2a-performance"}, "Runtime": "python3.13", "Architectures": ["arm64"], "Timeout": 30, "MemorySize": 2048, "CodeUri": {"Bucket": {"Fn::Join": ["", ["rk-deploy-jenkins-", {"Fn::ImportValue": "rk-footprint:stage"}]]}, "Key": {"Ref": "LambdaCheckmatCurveDeploy"}}, "Environment": {"Variables": {"POWERTOOLS_SERVICE_NAME": "checkmat-curve-service", "AWS_STAGE": {"Fn::ImportValue": "rk-footprint:stage"}, "SENTRY_DSN": {"Fn::Sub": "${SentryDSN}"}, "SENTRY_RELEASE": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, {"Fn::ImportValue": "performance-basic-infrastructure:Stage"}, {"Fn::Sub": "${SentryReleaseDate}"}]]}}}, "Handler": "main.handler"}}, "CheckmatCurveRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": "checkmat-curve-service-role", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", {"Ref": "PerformanceAnalyticsServiceFaultDynamoDbPolicy"}]}}, "LambdaCheckmatCurvePermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:invokeFunction", "FunctionName": {"Ref": "LambdaCheckmatCurveAliaslive"}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "/*"]]}}}, "ApiDeployment%%TIMESTAMP%%": {"Type": "AWS::ApiGateway::Deployment", "DependsOn": ["OverviewPostMethod", "OverviewOptionsMethod", "MachineDetailsPost", "MachineDetailsOptions", "ThresholdPostMethod", "ThresholdPutMethod", "ThresholdOptionsMethod", "TrendAnalysisV2PostMethod", "TrendAnalysisV2OptionsMethod", "MachineReportProxyMethod", "MachineReportOptionsMethod", "UnitsReportProxyMethod", "UnitsReportOptionsMethod", "ProductsOptionsMethod", "ProductsProxyMethod", "SpeedsOptionsMethod", "SpeedsProxyMethod", "CustomerSettingsGetMethod", "CustomerSettingsPostMethod", "CustomerSettingsOptionsMethod", "ErrorListGetMethod", "ErrorListOptionsMethod", "CommentsGetMethod", "CommentsPostMethod", "CommentsOptionsMethod", "CommentIdPutMethod", "CommentIdDeleteMethod", "CommentsIdOptionsMethod", "CheckmatCurveGetMethod", "CheckmatCurveOptionsMethod"], "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "StageName": {"Fn::ImportValue": "rk-footprint:apigateway-stage"}}}, "PerformanceAnalyticsServiceRootResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Fn::ImportValue": "rk-footprint:apigateway-root-resource"}, "PathPart": "performance-analytics"}}, "MachineReportResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "machine-report"}}, "MachineReportProxyResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "MachineReportResource"}, "PathPart": "{proxy+}"}}, "TrendAnalysisResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "trend-analysis"}}, "TrendAnalysisV2Resource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "TrendAnalysisResource"}, "PathPart": "v2"}}, "StoppageAnalysisResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "stoppage-analysis"}}, "MachineDetailsResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "StoppageAnalysisResource"}, "PathPart": "machine-details"}}, "OverviewResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "StoppageAnalysisResource"}, "PathPart": "overview"}}, "TrendAnalysisV2PostMethod": {"DependsOn": "LambdaPerformanceAnalyticsKpiServicePermission", "Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "POST", "ResourceId": {"Ref": "TrendAnalysisV2Resource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaPerformanceAnalyticsKpiServiceAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "TrendAnalysisV2OptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "OPTIONS", "ResourceId": {"Ref": "TrendAnalysisV2Resource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "NONE", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'POST,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "OverviewPostMethod": {"DependsOn": "LambdaPerformanceAnalyticsServicePermission", "Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "POST", "ResourceId": {"Ref": "OverviewResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaPerformanceAnalyticsServiceAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "OverviewOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "OPTIONS", "ResourceId": {"Ref": "OverviewResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "NONE", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'POST,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "MachineDetailsPost": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaPerformanceAnalyticsServicePermission", "Properties": {"HttpMethod": "POST", "ResourceId": {"Ref": "MachineDetailsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaPerformanceAnalyticsServiceAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "MachineDetailsOptions": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'POST,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "ResourceId": {"Ref": "MachineDetailsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "MachineReportProxyMethod": {"DependsOn": "LambdaPerformanceAnalyticsKpiServicePermission", "Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "ANY", "ResourceId": {"Ref": "MachineReportProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaPerformanceAnalyticsKpiServiceAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "MachineReportOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "OPTIONS", "ResourceId": {"Ref": "MachineReportProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "NONE", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'POST,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "CustomerSettingsResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "customer-settings"}}, "UnitsReportResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "units-report"}}, "UnitsReportProxyResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "UnitsReportResource"}, "PathPart": "{proxy+}"}}, "ProductsResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "products"}}, "ProductsProxyResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "ProductsResource"}, "PathPart": "{proxy+}"}}, "SpeedsResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "speeds"}}, "SpeedsProxyResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "SpeedsResource"}, "PathPart": "{proxy+}"}}, "ThresholdResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "StoppageAnalysisResource"}, "PathPart": "threshold"}}, "TouchMessagesResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "StoppageAnalysisResource"}, "PathPart": "touch-messages"}}, "FailureModesResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "StoppageAnalysisResource"}, "PathPart": "failure-modes"}}, "FailureModesIdResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "FailureModesResource"}, "PathPart": "{failure_mode_id}"}}, "CommentsResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "comments"}}, "ErrorListResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "error-list"}}, "ErrorListProxyId": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "ErrorListResource"}, "PathPart": "{proxy+}", "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}}}, "CommentIdResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "CommentsResource"}, "PathPart": "{comment_id}"}}, "CheckmatCurveResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "PathPart": "checkmat-curve"}}, "CheckmatCurveEquipmentIdResource": {"Type": "AWS::ApiGateway::Resource", "Properties": {"RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "ParentId": {"Ref": "CheckmatCurveResource"}, "PathPart": "{equipment_id}"}}, "UnitsReportProxyMethod": {"DependsOn": "LambdaUnitsReportServicePermission", "Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "ANY", "ResourceId": {"Ref": "UnitsReportProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaUnitsReportServiceAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "501", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "UnitsReportOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "OPTIONS", "ResourceId": {"Ref": "UnitsReportProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "NONE", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'GET,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "ThresholdPostMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaThresholdPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "POST", "ResourceId": {"Ref": "ThresholdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaThresholdAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "ThresholdPutMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaThresholdPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "PUT", "ResourceId": {"Ref": "ThresholdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaThresholdAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "ThresholdOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'POST,PUT,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "ResourceId": {"Ref": "ThresholdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "ProductsOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'GET,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "ResourceId": {"Ref": "ProductsProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "ProductsProxyMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaProductsSpeedsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "ANY", "ResourceId": {"Ref": "ProductsProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaProductsSpeedsAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "SpeedsOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'GET,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "ResourceId": {"Ref": "SpeedsProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "SpeedsProxyMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaProductsSpeedsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "ANY", "ResourceId": {"Ref": "SpeedsProxyResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaProductsSpeedsAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "CustomerSettingsGetMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaCustomerSettingsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "GET", "ResourceId": {"Ref": "CustomerSettingsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaCustomerSettingsAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "CustomerSettingsPostMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaCustomerSettingsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "POST", "ResourceId": {"Ref": "CustomerSettingsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaCustomerSettingsAliaslive"}, "/invocations"]]}, "IntegrationResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 400.*"}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 401.*"}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 403.*"}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 404.*"}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\": 500.*"}]}, "MethodResponses": [{"StatusCode": "200", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "400", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "401", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "403", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "404", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}, {"StatusCode": "500", "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": true}}]}}, "CustomerSettingsOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'GET,POST,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "ResourceId": {"Ref": "CustomerSettingsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "ErrorListGetMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaErrorListPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "GET", "ResourceId": {"Ref": "ErrorListProxyId"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaErrorListAliaslive"}, "/invocations"]]}}}}, "ErrorListOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'GET,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "ResourceId": {"Ref": "ErrorListProxyId"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "CommentsGetMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaCommentsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "GET", "ResourceId": {"Ref": "CommentsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaCommentsAliaslive"}, "/invocations"]]}}}}, "CommentsPostMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaCommentsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "POST", "ResourceId": {"Ref": "CommentsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaCommentsAliaslive"}, "/invocations"]]}}}}, "CommentsOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'GET,POST,OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "ResourceId": {"Ref": "CommentsResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "CommentIdPutMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaCommentsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "PUT", "ResourceId": {"Ref": "CommentIdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaCommentsAliaslive"}, "/invocations"]]}}}}, "CommentIdDeleteMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaCommentsPermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "DELETE", "ResourceId": {"Ref": "CommentIdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaCommentsAliaslive"}, "/invocations"]]}}}}, "CommentsIdOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "OPTIONS", "ResourceId": {"Ref": "CommentIdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "NONE", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'PUT, DELETE, OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "CheckmatCurveGetMethod": {"Type": "AWS::ApiGateway::Method", "DependsOn": "LambdaCheckmatCurvePermission", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Fn::ImportValue": "rk-footprint:apigateway-authorizer-s2a"}, "HttpMethod": "GET", "ResourceId": {"Ref": "CheckmatCurveEquipmentIdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "Integration": {"Type": "AWS_PROXY", "IntegrationHttpMethod": "POST", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:", {"Ref": "AWS::Region"}, ":lambda:path/2015-03-31/functions/", {"Ref": "LambdaCheckmatCurveAliaslive"}, "/invocations"]]}}}}, "CheckmatCurveOptionsMethod": {"Type": "AWS::ApiGateway::Method", "Properties": {"HttpMethod": "OPTIONS", "ResourceId": {"Ref": "CheckmatCurveEquipmentIdResource"}, "RestApiId": {"Fn::ImportValue": "rk-footprint:apigateway-id"}, "AuthorizationType": "NONE", "Integration": {"IntegrationResponses": [{"StatusCode": "200", "ResponseTemplates": {"application/json": ""}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'GET, OPTIONS'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"}}], "PassthroughBehavior": "NEVER", "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "MethodResponses": [{"StatusCode": "200", "ResponseModels": {"application/json": "Empty"}, "ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}}]}}, "CustomerSettingsTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": "rk-performance-customer-settings", "AttributeDefinitions": [{"AttributeName": "account", "AttributeType": "S"}, {"AttributeName": "line_id", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "account", "KeyType": "HASH"}, {"AttributeName": "line_id", "KeyType": "RANGE"}], "BillingMode": "PAY_PER_REQUEST", "SSESpecification": {"KMSMasterKeyId": {"Fn::ImportValue": "performance-shared-services-dynamodb-kms-key-alias"}, "SSEEnabled": true, "SSEType": "KMS"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}}}, "DynamoCustomerSettingsTablePolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "rk-customer-settings-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:Query", "dynamodb:UpdateItem"], "Resource": {"Fn::GetAtt": ["CustomerSettingsTable", "<PERSON><PERSON>"]}}]}}}, "DynamoThresholdTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": "rk-threshold-config", "AttributeDefinitions": [{"AttributeName": "line_id", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "line_id", "KeyType": "HASH"}], "BillingMode": "PAY_PER_REQUEST", "SSESpecification": {"KMSMasterKeyId": {"Fn::ImportValue": "performance-shared-services-dynamodb-kms-key-alias"}, "SSEEnabled": true, "SSEType": "KMS"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}}}, "DynamoThresholdTablePolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "rk-threshold-config-table-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:Query", "dynamodb:UpdateItem"], "Resource": {"Fn::GetAtt": ["DynamoThresholdTable", "<PERSON><PERSON>"]}}]}}}, "DynamoProductsSpeedsTablePolicy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "products-speeds-service-kpi-fault-table-read-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:GetItem", "dynamodb:Query"], "Resource": [{"Fn::ImportValue": {"Fn::Sub": "${DataStorageDynamoDB}:dynamodb-performance-data-arn"}}]}]}}}, "ProductsSpeedsS3Policy": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"ManagedPolicyName": "products-speeds-service-s3-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["s3:GetObject"], "Resource": {"Fn::Join": ["", ["arn:aws:s3:::rk-zait-", {"Fn::ImportValue": "rk-footprint:stage"}, "/*"]]}}]}}}}, "Parameters": {"LambdaPerformanceAnalyticsServiceDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaThresholdDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaKpiServiceDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaKpiServicePublicAPIDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaProductsSpeedsDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaCustomerSettingsDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaUnitsReportServiceDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaConfigGeneratorDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaCommentsDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaErrorListDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "LambdaCheckmatCurveDeploy": {"Type": "String", "Default": "dummy.zip", "Description": "S3Key to load the lambda function from."}, "RdsStackName": {"Description": "The RDS stack name", "Type": "String", "Default": "performance-rds"}, "MessageTextCrossAccountSSMRoleARN": {"Description": "arn of role for reading api key for meda message api", "Type": "String"}, "DataStorageDynamoDB": {"Description": "The new DynamoDB datastorage", "Type": "String", "Default": "performance-dynamodb"}, "SentryDSN": {"Description": "Sentry DSN", "Type": "String", "Default": "https://<EMAIL>/18"}, "SentryReleaseDate": {"Description": "Sentry Release Date", "Type": "String", "Default": "2023-01-01", "AllowedPattern": "^\\d{4}(-\\d{2}){2}$", "ConstraintDescription": "Date of creation"}}, "Outputs": {"PerformanceAnalyticsServiceRootResource": {"Description": "Performance Root API resource.", "Value": {"Ref": "PerformanceAnalyticsServiceRootResource"}, "Export": {"Name": "PerformanceAnalyticsServiceRootResource"}}}}